{"name": "vbee-tts-stats", "version": "2.0.0", "scripts": {"build": "tsc || echo \"Build completed with errors but files were generated\"", "prebuild": "echo Building TypeScript...", "prestart": "npm run build", "start": "node dist/index.js", "dev": "ts-node-dev --project tsconfig.dev.json --respawn --transpile-only src/index.ts", "dev:debug": "ts-node-dev --project tsconfig.dev.json --inspect --respawn --transpile-only src/index.ts", "test": "vitest"}, "author": "", "license": "ISC", "engines": {"node": ">=20.19"}, "dependencies": {"@gurucore/lakdak": "^0.15.6", "@vbee-holding/node-logger": "^2.0.0", "@vbee-holding/vbee-node-shared-lib": "^2.1.0", "@vbee-holding/vbee-tts-models": "^0.0.8", "@vbee-holding/vbee-tts-node-datastore": "^0.0.8", "aes-js": "^3.1.2", "axios": "0.24.0", "camelcase-keys": "7.0.1", "chardet": "^2.0.0", "cheerio": "^1.0.0-rc.12", "compression": "1.7.4", "cors": "2.8.5", "cron": "^2.2.0", "cross-env": "7.0.3", "cross-fetch": "^4.0.0", "dotenv": "10.0.0", "eventsource": "^2.0.2", "express": "4.17.1", "express-mung": "0.5.1", "express-validation": "3.0.8", "franc": "^6.1.0", "helmet": "4.6.0", "husky": "7.0.4", "iconv-lite": "^0.6.3", "jsonwebtoken": "^9.0.2", "kafkajs": "^2.2.4", "md5": "^2.3.0", "moment": "^2.29.4", "mongoose": "7.7.0", "morgan": "1.10.0", "node-rsa": "^1.1.1", "nodemon": "^2.0.22", "redis": "^5.6.1", "retry": "^0.13.1", "snakecase-keys": "5.1.0", "uuid": "^8.3.2", "yargs": "^17.7.2"}, "devDependencies": {"@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.2.0", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "eslint": "7.32.0", "eslint-config-airbnb-base": "14.2.1", "eslint-config-prettier": "8.3.0", "eslint-plugin-import": "2.25.2", "eslint-plugin-prettier": "4.0.0", "lint-staged": "11.2.6", "prettier": "2.4.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.9.2", "vitest": "^3.2.4"}}