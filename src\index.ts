/* eslint-disable import/order */
require('dotenv').config()

import logger from './utils/logger'
import Caching from './services/caching'

const { initMongoDB } = require('./models')
import RemoteConfigRepo from './services/growthbook'

const { RandomFactory } = require('./utils/random')

global.logger = logger // ADVISE: logger can be removed, no direct usage to this global var
// ADVISE: avoid using global (it hurts intellisense, code analysis tools, ...) if possible (should  import/export from a singleton module).
global.KAFKA_CONSUMER_GROUP_RANDOM_TTS = RandomFactory.getGuid()
// ADVISE: remove this and use cache for Voices
/** global array of Voices, act as a memory cache */
global.VOICES = null
global.LANGUAGES = null
global.SAMPLE_SCRIPTS = null
global.REQUEST_DIRECT = {}
global.AWS_ZONES_TTS_CACHING = {}
global.AWS_ZONES_TTS_STUDIO = {}
global.APPS = {}

const { initIAM } = require('./services/init/iam')
const { initApps } = require('./services/init/apps')
const { initLanguages } = require('./services/init/languages')
const { initVoices } = require('./services/init/voices')
const { initTtsCachingAwsZone, initTtsStudioAwsZone } = require('./services/init/awsZone')

import { initHttpServer } from './express'

// to be able to use async at top level, all app setup are in Immediately Invoked Function Expression (IIFE)
;(async () => {
  /**
   * CriticalInitizations: crash the process if cannot initilize (Mongo, Cache, Growthbook, initIAM)
   * OneTimeInitializations: log error (send instant notification) and continue.
   * RecurringInitializations: periodically run process (scheduleTokenRefresh)
   */
  try {
    logger.info('\n\n= = = = = = CriticalInitializations: Start', {
      ctx: 'CriticalInitializations',
    })
    await Promise.all([
      //
      RemoteConfigRepo.init(),
      Caching.init(),
      initMongoDB(),
      initIAM(),
    ])
  } catch (error) {
    logger.error(error, { ctx: 'CriticalInitializations' })
    process.exit(1)
  }

  try {
    logger.info('\n\n= = = = = = OneTimeInitializations: Start', {
      ctx: 'OneTimeInitializations',
    })
    await Promise.all([initVoices(), initLanguages(), initApps(), initTtsCachingAwsZone(), initTtsStudioAwsZone()])
  } catch (error) {
    // accept init failed
    logger.error(error, { ctx: 'OneTimeInitializations' })
  }

  try {
    logger.info('\n\n= = = = = = RecurringInitializations: Start', {
      ctx: 'RecurringInitializations',
    })
    await Promise.allSettled([
      //
    ])
  } catch (error) {
    // accept init failed
    logger.error(error, { ctx: 'RecurringInitializations' })
  }

  require('./services/kafka')

  await initHttpServer()

  logger.info('\n\n= = = = = = Initializations: Finished', {
    ctx: 'Initializations',
  })
})()
