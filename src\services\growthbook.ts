import configs from '../configs'
const { GROWTH_BOOK_API_HOST, GROWTH_BOOK_CLIENT_KEY, LOADING_FEATURES_REALTIME_INTERVAL } = configs
import logger from '../utils/logger'

import { GrowthBookAdapter } from '@vbee-holding/vbee-tts-node-datastore'

let GrowthBookSingleton: GrowthBookAdapter

/** init the RemoteConfig with GrowthBook, and kickoff the Polling Approach */
const init = async () => {
  GrowthBookSingleton = new GrowthBookAdapter({
    apiHost: GROWTH_BOOK_API_HOST,
    clientKey: GROWTH_BOOK_CLIENT_KEY,
    logger,
  })

  await GrowthBookSingleton.init()
  await GrowthBookSingleton.scheduleRefreshFeatures(LOADING_FEATURES_REALTIME_INTERVAL)
  logger.info('GrowthBook: kick off the scheduler to refresh features using Polling Approach', {
    ctx: 'GrowthBook',
  })

  logger.info('GrowthBook: Init successfully,', {
    ctx: 'GrowthBook',
    apiHost: GROWTH_BOOK_API_HOST,
  })
}

/** get CACHED feature value from the RemoteConfigRepository (cached with local instance to avoid calling to remote API all the times) */
export const getFeatureValue = (featureKey: string, attributes?: any, defaultValue?: any) => {
  if (!GrowthBookSingleton) {
    throw new Error('GrowthBook not initialized. Please call init() first.')
  }
  return GrowthBookSingleton.getValue(featureKey, attributes, defaultValue)
}

export default {
  GrowthBookSingleton,
  init,
}
