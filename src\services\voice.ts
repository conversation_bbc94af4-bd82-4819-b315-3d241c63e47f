const voiceDao = require('../daos/voice')
const { getSortQuery } = require('../daos/utils/util')
const { findLanguages } = require('../daos/language')
import configs from '../configs'
const {
  // ADVISE: not exist config, check for the correctness of the usage in this file
  API_TTS_VERSION_DEFAULT,
  STUDIO_TTS_VERSION_DEFAULT,
  VC_SYNTHESIS_FUNCTION,
  VC_ZERO_SHOT_CLOUD_RUN_URL,
  IS_TTS_GATE,
} = configs
import { SYNTHESIS_TYPE, REQUEST_TYPE, REGEX, TTS_CORE_VERSION, VOICE_TYPE } from '../constants'
import FEATURE_KEYS from '../constants/featureKeys'
const CustomError = require('../errors/CustomError')
const errorCode = require('../errors/code')
const { executeCallApiCreateVoice } = require('./ttsCore')
import { getFeatureValue } from './growthbook'

/** get Voice DTO by code */
const getVoiceByCode = async (code) => {
  if (!global.VOICES) {
    const { voices } = await voiceDao.findVoices();
    global.VOICES = voices;
  }

  let voiceExist = global.VOICES.find((voice) => voice.code === code);
  if (!voiceExist) {
    voiceExist = await voiceDao.findVoiceByCode(code);
    if (voiceExist) global.VOICES.push(voiceExist);
  }

  return voiceExist;
};

const createVoice = async ({
  code,
  name,
  image,
  gender,
  languageCode,
  provider,
  squareImage,
  roundImage,
  sampleRates,
  defaultSampleRate,
  global,
  synthesisFunction,
  type,
}) => {
  const checkVoiceExist = await getVoiceByCode(code);
  if (checkVoiceExist) throw new CustomError(errorCode.VOICE_EXIST);

  const voiceInfo = {
    code,
    name,
    image,
    gender,
    languageCode,
    provider,
    squareImage,
    roundImage,
    sampleRates,
    defaultSampleRate,
    global,
    synthesisFunction: synthesisFunction || VC_SYNTHESIS_FUNCTION,
    type,
  };

  if (type === VOICE_TYPE.ZERO_SHOT) voiceInfo['cloudRun.url'] = VC_ZERO_SHOT_CLOUD_RUN_URL;

  const voice = await voiceDao.createVoice(voiceInfo);

  // update global VOICES
  if (VOICES?.length) VOICES.push(voice);

  if (IS_TTS_GATE) {
    await executeCallApiCreateVoice({
      code,
      name,
      image,
      gender,
      languageCode,
      provider,
      squareImage,
      roundImage,
      sampleRates,
      defaultSampleRate,
      global,
      synthesisFunction,
    });
  }

  return voice;
};

const createVoices = async (voices) => {
  const newVoices = await voiceDao.createVoices(voices);

  const { voices: updatedVoices } = await voiceDao.findVoices();
  global.VOICES = updatedVoices;

  return newVoices;
};

const updateVoice = async (voiceId, updateFields) => {
  const voice = await voiceDao.updateVoice(voiceId, updateFields);

  const { voices } = await voiceDao.findVoices();
  global.VOICES = voices;

  return voice;
};

const getVoiceByCodes = async (codes) => {
  if (!VOICES) {
    const { voices } = await voiceDao.findVoices();
    global.VOICES = voices;
  }
  const voices = VOICES.filter((voice) => codes.includes(voice.code));
  return voices;
};

const findVoices = async (query = {}) => {
  const {
    search,
    searchFields = ['name'],
    query: queryField,
    offset = 0,
    limit,
    fields = [],
    sort = ['rank_asc'],
  } = query;

  let dataQuery = {};

  if (!VOICES?.length) {
    const { voices } = await voiceDao.findVoices();
    global.VOICES = voices;
  }
  let voices = VOICES;

  if (queryField) {
    const { gender, languageCode, features, level, ...otherQuery } = queryField;
    dataQuery = { ...otherQuery };

    const languageCodeArray = languageCode?.split(',') || [];
    const genderArray = gender?.split(',') || [];
    const featuresArray = features?.split(',') || [];
    const levelArray = level?.split(',') || [];

    voices = voices.filter(
      (voice) =>
        (!languageCode || languageCodeArray.includes(voice.languageCode)) &&
        (!gender || genderArray.includes(voice.gender)) &&
        (!level || levelArray.includes(voice.level)) &&
        (!features || featuresArray.some((feature) => voice.features?.includes(feature))),
    );
  }

  const searchRegex = new RegExp(search, 'gi');

  // filter by query
  for (const key in dataQuery) {
    if (queryField[key]) {
      voices = voices.filter((voice) => voice[key] === queryField[key]);
    }
  }

  voices = voices.filter((voice) => {
    let check = false;
    for (const key of searchFields) {
      check = check || voice[key].match(searchRegex);
    }
    return check;
  });

  const total = voices.length;

  // get sort
  const sortQuery = getSortQuery(sort);
  voices = voices.sort((a, b) => {
    let compare;
    for (const [key, value] of Object.entries(sortQuery)) {
      if (value === 1) {
        compare = compare || a[key] - b[key];
      } else compare = compare || b[key] - a[key];
    }

    return compare;
  });

  // limit and offset
  if (limit) voices = voices.slice(offset, offset + limit);
  else voices = voices.slice(offset);

  // select fields
  if (fields && fields.length > 0) {
    voices = voices.map((voice) => {
      const newVoice = {};
      for (const key of fields) {
        newVoice[key] = voice[key];
      }
      return newVoice;
    });
  }

  if (!LANGUAGES) {
    const { languages } = await findLanguages();
    global.LANGUAGES = languages;
  }

  const detailVoices = voices.map((voice) => {
    const language = LANGUAGES.find((item) => item.code === voice.languageCode);
    return { ...voice, language };
  });

  return { voices: detailVoices, total };
};

/** get voice and ttsCoreVersion */
const getVersionVoice = async ({ requestType, synthesisType, voiceCode, text }) => {
  const defaultVersion = requestType === REQUEST_TYPE.API ? API_TTS_VERSION_DEFAULT : STUDIO_TTS_VERSION_DEFAULT;
  if (synthesisType === SYNTHESIS_TYPE.MULTI_VOICE) return { ttsCoreVersion: defaultVersion };

  const voice = await getVoiceByCode(voiceCode);
  if (!voice) throw new CustomError(errorCode.INVALID_VOICE_CODE);

  const useEmphasisV2 = getFeatureValue(FEATURE_KEYS.EMPHASIS_FEATURE_V2, {
    voiceCode,
  });

  const checkEmphasisInText = !useEmphasisV2 && REGEX.ADVANCE_TAG.test(text);
  const versionNonEmphasis = checkEmphasisInText ? TTS_CORE_VERSION.NEW : TTS_CORE_VERSION.OLD;

  const ttsCoreVersion = versionNonEmphasis || voice?.version || defaultVersion;

  return { voice, ttsCoreVersion };
};

module.exports = {
  createVoice,
  getVoiceByCode,
  createVoices,
  updateVoice,
  getVoiceByCodes,
  findVoices,
  getVersionVoice,
};
