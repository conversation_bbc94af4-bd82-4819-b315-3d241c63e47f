import {
  RESPONSE_TYPE,
  SYNTHESIS_TYPE,
  TTS_CORE_VERSION,
  REGEX,
  REQUEST_TYPE,
  REQUEST_STATUS,
  AUDIO_TYPE,
  REDIS_KEY_PREFIX,
  SERVICE_TYPE,
  DEFAULT_RETENTION_PERIOD,
  TTS_CORE_COMPUTE_PLATFORM,
} from '../constants';
const code = require('../errors/code');
const CustomError = require('../errors/CustomError');
const { getVoiceByCode, getVersionVoice } = require('./voice');
const {
  validateText,
  getSynthesisType,
  countTextLength,
  getValidSampleRates,
  preCheckSynthesisApiRequest,
} = require('./preprocessing');
const { createRequest, createRequestInRedis, createRequestTitle } = require('./request');
const { sendPendingRequestToKafka } = require('./queue');
const { getAwsZone } = require('../daos/awsZone');
import configs from '../configs';
const {
  DEFAULT_NORMALIZER_FUNCTION,
  DEFAULT_SENTENCE_TOKENIZER_FUNCTION,
  DEFAULT_NEW_SENTENCE_TOKENIZER_FUNCTION,
  DEFAULT_T2A_FUNCTION,
  DEFAULT_SYNTHESIS_FUNCTION,
  DEFAULT_JOIN_AUDIO_FUNCTION,
  DEFAULT_BUCKET_S3,
  DEFAULT_AWS_REGION,
  MULTI_ZONE,
} = configs;
import { getFeatureValue } from './growthbook';

import FEATURE_KEYS from '../constants/featureKeys';
const { updateDictionaryAndTextWithAcronyms } = require('./acronym');
const { handleJoinAudiosSuccessResponse, handleJoinAudiosFailureResponse } = require('./synthesis');
const { RandomFactory } = require('../utils/random');

// ADVISE: duplicate code
const getAwsZoneSynthesis = (awsZones) => {
  if (!MULTI_ZONE) return DEFAULT_AWS_REGION;

  const lengthAwsZones = awsZones.length;
  const randomIndex = Math.floor(Math.random() * lengthAwsZones);
  const awsZone = awsZones[randomIndex];

  return awsZone || DEFAULT_AWS_REGION;
};

/** create TTSRequest object */
const handleApiSynthesisRequest = async ({
  ip,
  app,
  title,
  responseType = RESPONSE_TYPE.INDIRECT,
  outputType,
  v3ApiType,
  callbackUrl,
  callbackUpdateProgressUrl,
  text,
  voiceCode,
  sentences = [],
  audioType = AUDIO_TYPE.MP3,
  bitrate = 128,
  sampleRate,
  speed = 1,
  backgroundMusic,
  fromVn,
  serviceType,
  clientUserId,
  retentionPeriod = DEFAULT_RETENTION_PERIOD,
  dictionary,
  clientPause,
  sessionId,
  synthesisCcr,
  returnTimestamp,
  synthesisComputePlatform,
  isPriority,
  isRealTime,
}) => {
  const requestId = RandomFactory.getGuid();
  const requestCreatedAt = new Date();

  const awsZoneSynthesis = getAwsZoneSynthesis(global.AWS_ZONES_TTS_STUDIO);

  const useReturnTimestamp = getFeatureValue(FEATURE_KEYS.TIMESTAMP_WORDS, {
    userId: clientUserId,
  });

  const synthesisType = getSynthesisType({ text, sentences });
  if (synthesisType === SYNTHESIS_TYPE.MULTI_VOICE)
    text = sentences.reduce((prev, curr) => prev + curr.text.trim(), '');

  const { voice, ttsCoreVersion } = await getVersionVoice({
    requestType: REQUEST_TYPE.API,
    synthesisType,
    voiceCode,
    text,
  });

  const useEmphasisV2 = getFeatureValue(FEATURE_KEYS.EMPHASIS_FEATURE_V2, {
    voiceCode,
  });

  if (ttsCoreVersion === TTS_CORE_VERSION.NEW && !useEmphasisV2)
    text = text.replace(
      REGEX.OLD_BREAK_TIME,
      (p1, p2) => `${`<break time="${p2}s"/>`}`,
    );

  if (useEmphasisV2)
    text = text.replace(
      REGEX.NEW_BREAK_TIME,
      (p1, p2) => `${`<break time=${p2}s/>`}`,
    );

  const { appId, textLength } = preCheckSynthesisApiRequest({
    app,
    text,
    ttsCoreVersion,
  });

  const awsZone = await getAwsZone({ region: awsZoneSynthesis }); // ADVISE: get from cache, auto fallback empty props to DEFAULT_XXX_FUNCTION
  const {
    normalizerFunction = DEFAULT_NORMALIZER_FUNCTION,
    sentenceTokenizerFunction = DEFAULT_SENTENCE_TOKENIZER_FUNCTION,
    newSentenceTokenizerFunction = DEFAULT_NEW_SENTENCE_TOKENIZER_FUNCTION,
    textToAllophoneFunction = DEFAULT_T2A_FUNCTION,
    synthesisFunction = DEFAULT_SYNTHESIS_FUNCTION,
    defaultS3Bucket = DEFAULT_BUCKET_S3,
    s3Buckets = {},
  } = awsZone;
  const joinSentencesFunction =
    getFeatureValue(FEATURE_KEYS.AUDIO_JOINER_NEW_FUNCTION, {
      clientUserId,
      awsZoneSynthesis,
    }) ||
    awsZone?.joinSentencesFunction ||
    DEFAULT_JOIN_AUDIO_FUNCTION;

  if (synthesisType === SYNTHESIS_TYPE.MULTI_VOICE) {
    const voiceCodes = sentences.reduce((acc, curr) => {
      if (curr.voiceCode && !acc.includes(curr.voiceCode))
        return [...acc, curr.voiceCode];
      return acc;
    }, []);

    const { sampleRates, maxSampleRate } = await getValidSampleRates(
      voiceCodes,
    );
    if (!maxSampleRate || (sampleRate && !sampleRates.includes(sampleRate)))
      throw new CustomError(
        code.INVALID_SAMPLE_RATE,
        `This voice only support sample rate of ${JSON.stringify(sampleRates)}`,
      );

    sentences = await Promise.all(
      sentences.map(async (sentence) => {
        const sentenceVoice = await getVoiceByCode(sentence.voiceCode);
        if (!sentenceVoice) throw new CustomError(code.INVALID_VOICE_CODE);
        if (sentenceVoice.version === TTS_CORE_VERSION.NEW)
          throw new CustomError(code.SENTENCES_NOT_SUPPORT_EMPHASIS);

        let sentenceTextLength;
        const sentenceText = sentence.text;
        const sentenceComputePlatform =
          sentence?.synthesisComputePlatform ||
          TTS_CORE_COMPUTE_PLATFORM.LAMBDA;
        const isValidText = validateText({
          text: sentenceText,
          voiceProvider: sentenceVoice.provider,
          ttsCoreVersion,
        });

        if (!isValidText) throw new CustomError(code.INVALID_SYNTAX);

        if (ttsCoreVersion === TTS_CORE_VERSION.NEW) {
          sentenceTextLength = countTextLength(sentenceText, REGEX.ADVANCE_TAG);
        } else {
          sentenceTextLength = countTextLength(sentenceText);
        }

        return {
          ...sentence,
          text: sentenceText,
          characters: sentenceTextLength,
          synthesisComputePlatform: sentenceComputePlatform,
        };
      }),
    );

    const request = {
      ip,
      requestId,
      title: title || sentences[0].text.slice(0, 30).trim(), // TODO: remove title
      sentences,
      characters: textLength,
      audioType,
      createdAt: requestCreatedAt,
      status: REQUEST_STATUS.IN_PROGRESS,
      voiceCode: sentences[0].voiceCode,
      bitrate,
      sampleRate: sampleRate ? sampleRate.toString() : maxSampleRate.toString(),
      retentionPeriod,
      version: ttsCoreVersion,
      app: appId,
      callbackUrl,
      callbackUpdateProgressUrl,
      responseType,
      outputType,
      v3ApiType,
      type: REQUEST_TYPE.API,
      fromVn,
      serviceType: serviceType || SERVICE_TYPE.AI_VOICE,
      clientUserId,
      awsZoneSynthesis,
      awsZoneFunctions: {
        normalizerFunction,
        sentenceTokenizerFunction,
        newSentenceTokenizerFunction,
        textToAllophoneFunction,
        synthesisFunction,
        joinSentencesFunction,
        s3Bucket: s3Buckets[retentionPeriod] || defaultS3Bucket,
      },
      dictionary,
      clientPause,
      sessionId,
      synthesisCcr,
      returnTimestamp: useReturnTimestamp ? returnTimestamp : false,
    };

    if (isRealTime) request.isRealTime = true;
    if (backgroundMusic) request.backgroundMusic = backgroundMusic;
    await createRequest(request);

    const firstVoice = await getVoiceByCode(sentences[0].voiceCode);
    request.voice = firstVoice;
    const cacheSentences = sentences.map((sentence) => {
      const { text: textSentence, ...cacheSentence } = sentence;
      return cacheSentence;
    });
    const cacheRequest = { ...request, sentences: cacheSentences, progress: 0 };
    const sentenceKeys = Array.from(Array(sentences.length).keys()).map(
      (index) => `${REDIS_KEY_PREFIX.SENTENCE}_${requestId}_${index}`,
    );
    cacheRequest.sentenceKeys = sentenceKeys;
    cacheRequest.numberOfIndexSentences = sentences.length;
    cacheRequest.numberOfSentences = sentences.length;
    await createRequestInRedis(cacheRequest);

    sendPendingRequestToKafka({ request: cacheRequest, userId: clientUserId });

    return {
      appId,
      requestId,
      characters: textLength,
      voiceCode,
      audioType,
      speedRate: speed,
      bitrate,
      sampleRate: request.sampleRate,
      backgroundMusic,
      status: REQUEST_STATUS.IN_PROGRESS,
    };
  }

  const isValidText = validateText({
    text,
    voiceProvider: voice.provider,
    ttsCoreVersion,
  });
  if (!isValidText) throw new CustomError(code.INVALID_SYNTAX);

  const { dictionaryWithAcronyms, text: outputText } =
    updateDictionaryAndTextWithAcronyms({
      requestId,
      text,
      dictionary,
      clientUserId,
      voiceProvider: voice.provider,
      appId,
    });

  if (sampleRate && !voice.sampleRates.includes(sampleRate))
    throw new CustomError(
      code.INVALID_SAMPLE_RATE,
      `This voice only support sample rate of ${JSON.stringify(
        voice.sampleRates,
      )}. ${sampleRate} is not supported.`,
    );

  const request = {
    ip,
    requestId,
    title: title || createRequestTitle(text), // TODO: remove title
    text,
    characters: textLength,
    audioType,
    speed,
    createdAt: requestCreatedAt,
    status: REQUEST_STATUS.IN_PROGRESS,
    voiceCode,
    bitrate,
    sampleRate: sampleRate
      ? sampleRate.toString()
      : voice.defaultSampleRate.toString(),
    retentionPeriod,
    version: ttsCoreVersion,
    app: appId,
    callbackUrl,
    callbackUpdateProgressUrl,
    responseType,
    outputType,
    v3ApiType,
    type: REQUEST_TYPE.API,
    fromVn,
    serviceType: serviceType || SERVICE_TYPE.AI_VOICE,
    clientUserId,
    awsZoneSynthesis,
    awsZoneFunctions: {
      normalizerFunction,
      sentenceTokenizerFunction,
      newSentenceTokenizerFunction,
      textToAllophoneFunction,
      synthesisFunction,
      joinSentencesFunction,
      s3Bucket: s3Buckets[retentionPeriod] || defaultS3Bucket,
    },
    dictionary: dictionaryWithAcronyms,
    clientPause,
    sessionId,
    synthesisCcr,
    returnTimestamp: useReturnTimestamp ? returnTimestamp : false,
    synthesisComputePlatform:
      synthesisComputePlatform || TTS_CORE_COMPUTE_PLATFORM.LAMBDA,
  };
  if (backgroundMusic) request.backgroundMusic = backgroundMusic;

  // Set priority request
  const isPriorityRequest = getFeatureValue(FEATURE_KEYS.PRIORITIZE_REQUEST, {
    userId: clientUserId,
    characters: textLength,
    voiceCode: voice.code,
  });
  if (isPriority && isPriorityRequest) request.isPriority = true;

  if (isRealTime) request.isRealTime = true;

  await createRequest(request);

  const { text: textRequest, ...cacheRequest } = request;
  cacheRequest.numberOfSentences = 0;
  cacheRequest.voice = voice;
  cacheRequest.progress = 0;
  cacheRequest.sentenceKeys = [
    `${REDIS_KEY_PREFIX.SENTENCE}_${requestId}_${0}`,
  ];
  cacheRequest.numberOfIndexSentences = 1;
  await createRequestInRedis(cacheRequest); // ADVISE: BUSINESS: cache without text, why?

  sendPendingRequestToKafka({
    request: cacheRequest, // ADVISE: BUSINESS, send request to kafka without text
    userId: clientUserId,
    textAfterHandleAcronym: outputText,
  });

  return {
    appId,
    requestId,
    characters: textLength,
    voiceCode,
    audioType,
    speedRate: speed,
    bitrate,
    sampleRate: request.sampleRate,
    backgroundMusic,
    status: REQUEST_STATUS.IN_PROGRESS,
  };
};

const apiCallbackResponse = async ({
  requestId,
  ttsRequestId,
  status,
  audioLink,
  errorCode,
  errorMessage: error,
  timestampWords,
}) => {
  if (status) {
    await handleJoinAudiosSuccessResponse({
      requestId,
      ttsRequestId,
      audioLink,
      timestampWords,
    });
  } else {
    await handleJoinAudiosFailureResponse({
      requestId,
      ttsRequestId,
      error,
      errorCode,
    });
  }
};

/**
 *
 * @param {Array} sentences
 */
const standardizeSentences = (sentences, synthesisComputePlatform) => {
  let normalizedArr = [];
  if (sentences?.length > 0) {
    normalizedArr = sentences.map((sen) => ({
      text: sen.inputText,
      voiceCode: sen.voiceCode,
      speed: sen.speedRate,
      breakTime: sen.breakTime,
      synthesisComputePlatform:
        sen?.synthesisComputePlatform ||
        synthesisComputePlatform ||
        TTS_CORE_COMPUTE_PLATFORM.LAMBDA,
    }));
  }

  return normalizedArr;
};

module.exports = {
  standardizeSentences,

  handleApiSynthesisRequest,
  apiCallbackResponse,
};
